import { TimerColorPreset } from '@/lib/pomodoro-store';
import { usePomodoroStore } from '@/lib/pomodoro-store';

// Position classes for the timer
export const positionClasses = {
  'top-left': 'top-4 left-4',
  'top-right': 'top-4 right-4',
  'bottom-left': 'bottom-4 left-4',
  'bottom-right': 'bottom-4 right-4',
  'center': 'top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
  'top-center': 'top-4 left-1/2 -translate-x-1/2',
  'bottom-center': 'bottom-4 left-1/2 -translate-x-1/2',
  'middle-left': 'top-1/2 left-4 -translate-y-1/2',
  'middle-right': 'top-1/2 right-4 -translate-y-1/2',
  
  // Simplified bottom positions (5 positions)
  'bottom-left-center': 'bottom-4 left-[25%]',
  'bottom-right-center': 'bottom-4 left-[75%]',
};

// Bottom edge padding constant
export const BOTTOM_EDGE_PADDING = 16; // 16px padding from bottom edge

// Color utility functions
export const getButtonTextColor = (timerColor: TimerColorPreset) => {
  switch (timerColor) {
    case 'white': return 'text-white';
    case 'blue': return 'text-blue-400';
    case 'green': return 'text-green-400';
    case 'yellow': return 'text-amber-400';
    case 'red': return 'text-rose-400';
    case 'purple': return 'text-purple-400';
    case 'indigo': return 'text-indigo-400';
    case 'orange': return 'text-orange-400';
    case 'pink': return 'text-pink-400';
    default: return 'text-white';
  }
};

export const getButtonHoverEffect = (timerColor: TimerColorPreset) => {
  switch (timerColor) {
    case 'white': return 'hover:bg-white/15';
    case 'blue': return 'hover:bg-blue-500/15';
    case 'green': return 'hover:bg-green-500/15';
    case 'yellow': return 'hover:bg-amber-400/15';
    case 'red': return 'hover:bg-rose-500/15';
    case 'purple': return 'hover:bg-purple-500/15';
    case 'indigo': return 'hover:bg-indigo-500/15';
    case 'orange': return 'hover:bg-orange-500/15';
    case 'pink': return 'hover:bg-pink-500/15';
    default: return 'hover:bg-white/15';
  }
};

export const getButtonActiveColor = (timerColor: TimerColorPreset, isRunning: boolean) => {
  switch (timerColor) {
    case 'white': return isRunning ? 'bg-white/10' : '';
    case 'blue': return isRunning ? 'bg-blue-500/10' : '';
    case 'green': return isRunning ? 'bg-green-500/10' : '';
    case 'yellow': return isRunning ? 'bg-amber-400/10' : '';
    case 'red': return isRunning ? 'bg-rose-500/10' : '';
    case 'purple': return isRunning ? 'bg-purple-500/10' : '';
    case 'indigo': return isRunning ? 'bg-indigo-500/10' : '';
    case 'orange': return isRunning ? 'bg-orange-500/10' : '';
    case 'pink': return isRunning ? 'bg-pink-500/10' : '';
    default: return isRunning ? 'bg-white/10' : '';
  }
};

export const getProgressBarColor = (timerColor: TimerColorPreset) => {
  const currentPhase = usePomodoroStore.getState().currentPhase;

  // Base on timer color setting first
  switch (timerColor) {
    case 'blue':
      return 'bg-gradient-to-r from-blue-500 to-blue-400';
    case 'green':
      return 'bg-gradient-to-r from-emerald-500 to-emerald-400';
    case 'yellow':
      return 'bg-gradient-to-r from-amber-400 to-amber-300';
    case 'red':
      return 'bg-gradient-to-r from-rose-500 to-rose-400';
    case 'purple':
      return 'bg-gradient-to-r from-purple-500 to-purple-400';
    case 'indigo':
      return 'bg-gradient-to-r from-indigo-500 to-indigo-400';
    case 'orange':
      return 'bg-gradient-to-r from-orange-500 to-orange-400';
    case 'pink':
      return 'bg-gradient-to-r from-pink-500 to-pink-400';
    case 'white':
    default:
      // White uses phase-specific colors for better visual feedback
      switch (currentPhase) {
        case 'pomodoro':
          return 'bg-gradient-to-r from-blue-500 to-blue-400';
        case 'shortBreak':
          return 'bg-gradient-to-r from-emerald-500 to-emerald-400';
        case 'longBreak':
          return 'bg-gradient-to-r from-purple-500 to-purple-400';
        default:
          return 'bg-gradient-to-r from-blue-500 to-blue-400';
      }
  }
};

export const getTimerTextColor = (timerColor: TimerColorPreset) => {
  switch (timerColor) {
    case 'blue':
      return 'text-blue-400';
    case 'green':
      return 'text-emerald-400';
    case 'yellow':
      return 'text-amber-400';
    case 'red':
      return 'text-rose-400';
    case 'purple':
      return 'text-purple-400';
    case 'indigo':
      return 'text-indigo-400';
    case 'orange':
      return 'text-orange-400';
    case 'pink':
      return 'text-pink-400';
    case 'white':
    default:
      // For white mode, keep countdown text consistently WHITE
      return 'text-white';
  }
};

// New function for phase label colors - white option uses white for all labels
export const getPhaseLabelColor = (timerColor: TimerColorPreset) => {
  switch (timerColor) {
    case 'blue':
      return 'text-blue-400';
    case 'green':
      return 'text-emerald-400';
    case 'yellow':
      return 'text-amber-400';
    case 'red':
      return 'text-rose-400';
    case 'purple':
      return 'text-purple-400';
    case 'indigo':
      return 'text-indigo-400';
    case 'orange':
      return 'text-orange-400';
    case 'pink':
      return 'text-pink-400';
    case 'white':
    default:
      // For white mode, use white for all labels (monochromatic)
      return 'text-white';
  }
};

export const getTimerTextColorValue = (timerColor: TimerColorPreset) => {
  // Enhanced opacity (0.98) for better contrast against glass backgrounds
  // Slightly brighter colors for improved readability on glassmorphism
  switch (timerColor) {
    case 'blue': return 'rgba(96, 165, 250, 0.98)'; // blue-400 (brighter)
    case 'green': return 'rgba(52, 211, 153, 0.98)'; // emerald-400 (brighter)
    case 'yellow': return 'rgba(251, 191, 36, 0.98)'; // amber-400
    case 'red': return 'rgba(248, 113, 113, 0.98)'; // red-400 (brighter)
    case 'purple': return 'rgba(196, 181, 253, 0.98)'; // purple-300 (brighter)
    case 'indigo': return 'rgba(129, 140, 248, 0.98)'; // indigo-400 (brighter)
    case 'orange': return 'rgba(251, 146, 60, 0.98)'; // orange-400
    case 'pink': return 'rgba(244, 114, 182, 0.98)'; // pink-400
    case 'white':
    default:
      // For white mode, keep countdown text consistently WHITE with full opacity
      return 'rgba(255, 255, 255, 0.98)'; // white
  }
};

export const getTimerTextShadow = (timerColor: TimerColorPreset) => {
  const isRunning = usePomodoroStore.getState().isRunning;

  // Enhanced base shadow for better readability on glass backgrounds
  const baseShadow = '0 2px 4px rgba(0, 0, 0, 0.4), 0 1px 2px rgba(0, 0, 0, 0.2)';

  // Glass-optimized shadows with stronger contrast
  if (isRunning) {
    switch (timerColor) {
      case 'blue':
        return `${baseShadow}, 0 0 12px rgba(96, 165, 250, 0.4), 0 0 24px rgba(96, 165, 250, 0.2)`;
      case 'green':
        return `${baseShadow}, 0 0 12px rgba(52, 211, 153, 0.4), 0 0 24px rgba(52, 211, 153, 0.2)`;
      case 'yellow':
        return `${baseShadow}, 0 0 12px rgba(251, 191, 36, 0.4), 0 0 24px rgba(251, 191, 36, 0.2)`;
      case 'red':
        return `${baseShadow}, 0 0 12px rgba(248, 113, 113, 0.4), 0 0 24px rgba(248, 113, 113, 0.2)`;
      case 'purple':
        return `${baseShadow}, 0 0 12px rgba(196, 181, 253, 0.4), 0 0 24px rgba(196, 181, 253, 0.2)`;
      case 'indigo':
        return `${baseShadow}, 0 0 12px rgba(129, 140, 248, 0.4), 0 0 24px rgba(129, 140, 248, 0.2)`;
      case 'orange':
        return `${baseShadow}, 0 0 12px rgba(251, 146, 60, 0.4), 0 0 24px rgba(251, 146, 60, 0.2)`;
      case 'pink':
        return `${baseShadow}, 0 0 12px rgba(244, 114, 182, 0.4), 0 0 24px rgba(244, 114, 182, 0.2)`;
      case 'white':
      default:
        // For white mode, enhanced white glow for glass compatibility
        return `${baseShadow}, 0 0 12px rgba(255, 255, 255, 0.3), 0 0 24px rgba(255, 255, 255, 0.15)`;
    }
  }

  // Enhanced default shadow for glass backgrounds
  return baseShadow;
};

// New function for glass-optimized phase label colors with enhanced contrast
export const getGlassPhaseLabelColor = (timerColor: TimerColorPreset) => {
  switch (timerColor) {
    case 'blue':
      return 'text-blue-300'; // Lighter for better glass contrast
    case 'green':
      return 'text-emerald-300';
    case 'yellow':
      return 'text-amber-300';
    case 'red':
      return 'text-red-300';
    case 'purple':
      return 'text-purple-200'; // Even lighter for purple
    case 'indigo':
      return 'text-indigo-300';
    case 'orange':
      return 'text-orange-300';
    case 'pink':
      return 'text-pink-300';
    case 'white':
    default:
      // For white mode, use white for all labels (monochromatic)
      return 'text-white';
  }
};

// Enhanced text shadow specifically for phase labels on glass
export const getGlassLabelTextShadow = (timerColor: TimerColorPreset) => {
  // Stronger shadow for smaller text on glass backgrounds
  const baseShadow = '0 2px 4px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.3)';

  switch (timerColor) {
    case 'blue':
      return `${baseShadow}, 0 0 8px rgba(147, 197, 253, 0.3)`;
    case 'green':
      return `${baseShadow}, 0 0 8px rgba(110, 231, 183, 0.3)`;
    case 'yellow':
      return `${baseShadow}, 0 0 8px rgba(252, 211, 77, 0.3)`;
    case 'red':
      return `${baseShadow}, 0 0 8px rgba(252, 165, 165, 0.3)`;
    case 'purple':
      return `${baseShadow}, 0 0 8px rgba(196, 181, 253, 0.3)`;
    case 'indigo':
      return `${baseShadow}, 0 0 8px rgba(165, 180, 252, 0.3)`;
    case 'orange':
      return `${baseShadow}, 0 0 8px rgba(251, 146, 60, 0.3)`;
    case 'pink':
      return `${baseShadow}, 0 0 8px rgba(249, 168, 212, 0.3)`;
    case 'white':
    default:
      return `${baseShadow}, 0 0 8px rgba(255, 255, 255, 0.2)`;
  }
};

export const getBackgroundOpacity = (timerOpacity: number) => {
  // Return the exact opacity value set by user
  // No minimum threshold - allow full transparency if user wants it
  return timerOpacity;
};

export function getPhaseGradient() {
  const currentPhase = usePomodoroStore.getState().currentPhase;
  
  switch (currentPhase) {
    case 'pomodoro':
      return 'linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.06))';
    case 'shortBreak':
      return 'linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(5, 150, 105, 0.06))';
    case 'longBreak':
      return 'linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(124, 58, 237, 0.06))';
    default:
      return 'linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.06))';
  }
} 